<template>
  <div class="h-[100vh] flex items-center justify-center flex-col">
    <button class="p-2 mb-2 border" @click="postMessage">发送消息</button>
    <button class="p-2 mb-2 border" @click="dropLogin">跳转到登录页面</button>
  </div>
</template>

<script setup>
// 在这样式向app端发送消息，在H5中调用uni函数功能

const isWeb = location.href && /^https?:/.test(location.href);

function postMessage() {
  if (isWeb) {
    alert("web端暂不支持此功能");
    return;
  }
  uni.postMessage({
    // 这个data会传递给app端
    data: {
      action: "message",
    },
  });
}
/**
 * <web-view> 加载的网页中支持调用部分 uni 接口：
 * uni.navigateTo
 * uni.redirectTo
 * uni.reLaunch
 * uni.switchTab
 * uni.navigateBack
 * uni.postMessage	向应用发送消息	抖音小程序不支持、H5 暂不支持（可以直接使用 window.postMessage）
 * uni.getEnv	获取当前环境	抖音小程序与飞书小程序不支持
 */
function dropLogin() {
  uni.reLaunch({
    url: "/pages/commons/login",
  });
}
</script>

<style></style>
